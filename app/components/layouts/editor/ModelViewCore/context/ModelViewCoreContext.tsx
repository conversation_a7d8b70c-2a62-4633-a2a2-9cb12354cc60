import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useRef,
  useEffect,
  RefObject,
  useMemo,
} from 'react';
import { ControlFunctions, PartOperations } from '../types';
import { Part, PartCategory, PointerStyle } from '../../types';
import PartContextMenu from '../context/PartContextMenu';

// Add new type for interactive mode
interface InteractiveModePart {
  partId: string;
  isHighlighted: boolean;
  isIsolated: boolean;
  isHidden: boolean;
}

interface SelectedTool {
  mainTool: string;
  subTool?: string;
}

interface ModelViewCoreContextType {
  partsList: Part[];
  setPartsList: React.Dispatch<React.SetStateAction<Part[]>>;
  controlFunctions: ControlFunctions | null;
  setControlFunctions: React.Dispatch<
    React.SetStateAction<ControlFunctions | null>
  >;
  selectedPart: string | null;
  setSelectedPart: React.Dispatch<React.SetStateAction<string | null>>;
  containerRef: RefObject<HTMLDivElement | null>;
  scriptsLoaded: boolean;
  isLoaded: boolean;
  initializeModel: (htmlString: string) => (() => void) | undefined;

  // Tool and pointer awareness
  selectedTool: SelectedTool | null;
  setSelectedTool: (tool: SelectedTool) => void;
  currentPointerId: string | null;
  setCurrentPointerId: (id: string | null) => void;
  currentPointerStyle: PointerStyle | null;
  setCurrentPointerStyle: (style: PointerStyle | null) => void;
  updateCanvasCursor: () => void;
  // Add new functions for interactive mode
  saveInteractiveMode: () => void;
  loadInteractiveMode: (pointerId: string) => void;
  clearInteractiveMode: () => void;

  interactiveMode: InteractiveModePart[];
  setInteractiveMode: React.Dispatch<React.SetStateAction<InteractiveModePart[]>>;
  applyInteractiveMode: (mode: InteractiveModePart[]) => void;
  resetInteractiveMode: () => void;
}

const ModelViewCoreContext = createContext<
  ModelViewCoreContextType | undefined
>(undefined);

export const ModelViewCoreProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [partsList, setPartsList] = useState<Part[]>([]);
  const [controlFunctions, setControlFunctions] =
    useState<ControlFunctions | null>(null);
  const [selectedPart, setSelectedPart] = useState<string | null>(null);
  const [scriptsLoaded, setScriptsLoaded] = useState<boolean>(false);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const initializedRef = useRef<boolean>(false);

  // Tool and pointer state
  const [selectedTool, setSelectedToolState] = useState<SelectedTool | null>(
    null,
  );
  const [currentPointerId, setCurrentPointerIdState] = useState<string | null>(
    null,
  );
  const [currentPointerStyle, setCurrentPointerStyleState] =
    useState<PointerStyle | null>(null);
  const [interactiveMode, setInteractiveMode] = useState<InteractiveModePart[]>([]);

  // Add state for context menu
  const [contextMenu, setContextMenu] = useState<{
    isOpen: boolean;
    position: { x: number; y: number };
    partName: string;
  }>({
    isOpen: false,
    position: { x: 0, y: 0 },
    partName: '',
  });

  // Add state for tracking hidden and highlighted parts
  const [hiddenParts, setHiddenParts] = useState<Set<string>>(new Set());
  const [highlightedParts, setHighlightedParts] = useState<Set<string>>(
    new Set(),
  );
  const [isolatedPartName, setIsolatedPartName] = useState<string | null>(null);
  const [isolationMode, setIsolationMode] = useState(false);

  // Create a ref to store the latest partsList
  const partsListRef = useRef<Part[]>([]);

  // Update the ref whenever partsList changes
  useEffect(() => {
    partsListRef.current = partsList;
  }, [partsList]);

  // Basic utility functions
  const getElementById = useCallback((id: string): Element | null => {
    return containerRef.current?.querySelector(`#${id}`) || null;
  }, []);

  // Set selected tool and update cursor
  const setSelectedTool = useCallback((tool: SelectedTool) => {
    console.log('ModelViewCore: Setting tool:', tool);
    setSelectedToolState(tool);
  }, []);

  // Basic part operations
  const showPart = useCallback(
    (partName: string): void => {
      const part = partsListRef.current.find((p) => p.id === partName);
      if (!part) return;

      if (part.parentSwitchId) {
        const parent = getElementById(part.parentSwitchId) as HTMLElement;
        if (parent) {
          parent.setAttribute('whichChoice', '0');
        }
      }

      part.relatedSwitchIds?.forEach((switchId: string) => {
        const switchElement = getElementById(switchId) as HTMLElement;
        if (switchElement) {
          switchElement.setAttribute('whichChoice', '0');
        }
      });
    },
    [getElementById],
  );

  const hidePart = useCallback(
    (partName: string): void => {
      const part = partsListRef.current.find((p) => p.id === partName);
      if (!part) return;

      if (part.parentSwitchId) {
        const parent = getElementById(part.parentSwitchId) as HTMLElement;
        if (parent) {
          parent.setAttribute('whichChoice', '-1');
        }
      }

      part.relatedSwitchIds?.forEach((switchId: string) => {
        const switchElement = getElementById(switchId) as HTMLElement;
        if (switchElement) {
          switchElement.setAttribute('whichChoice', '-1');
        }
      });
    },
    [getElementById],
  );

  const highlightPart = useCallback(
    (partName: string, color: string = '1 1 0'): void => {
      const part = partsListRef.current.find((p) => p.id === partName);
      if (!part) {
        return;
      }

      const highlightSwitches = [
        part.parentSwitchId,
        ...(part.relatedSwitchIds || []),
      ];

      highlightSwitches.forEach((switchId) => {
        if (!switchId) return;

        const switchElement = getElementById(switchId);
        if (switchElement) {
          const shapes = Array.from(switchElement.querySelectorAll('shape'));

          shapes.forEach((shape) => {
            const appearance = shape.querySelector('appearance');

            if (appearance) {
              // Store original appearance for restoration
              if (!appearance.hasAttribute('data-original-appearance')) {
                appearance.setAttribute(
                  'data-original-appearance',
                  appearance.outerHTML,
                );
              }

              // Replace with new appearance containing highlight material
              const newAppearance = document.createElement('appearance');
              const material = document.createElement('material');
              material.setAttribute('diffusecolor', color);
              material.setAttribute('data-created-for-highlight', 'true');
              newAppearance.appendChild(material);

              shape.replaceChild(newAppearance, appearance);
            }
          });
        }
      });
    },
    [getElementById],
  );

  // Function to save interactive mode to pointer storage
  const savePointerInteractiveMode = useCallback((pointerId: string, mode: InteractiveModePart[]) => {
    localStorage.setItem(`pointer_${pointerId}_interactive_mode`, JSON.stringify(mode));
  }, []);

  // Function to load interactive mode from pointer storage
  const loadPointerInteractiveMode = useCallback((pointerId: string): InteractiveModePart[] | null => {
    const saved = localStorage.getItem(`pointer_${pointerId}_interactive_mode`);
    return saved ? JSON.parse(saved) : null;
  }, []);

  // Function to apply interactive mode to the model
  const applyInteractiveMode = useCallback((mode: InteractiveModePart[]) => {
    // First reset everything
    partsListRef.current.forEach(part => {
      showPart(part.id);
      removeHighlight(part.id);
    });

    // Find isolated part if any
    const isolatedPart = mode.find(m => m.isIsolated);
    if (isolatedPart) {
      // Hide all parts except the isolated one
      partsListRef.current.forEach(part => {
        if (part.id !== isolatedPart.partId) {
          hidePart(part.id);
        }
      });
    } else {
      // Apply hidden and highlighted states
      mode.forEach(({ partId, isHidden, isHighlighted }) => {
        if (isHidden) {
          hidePart(partId);
        }
        if (isHighlighted) {
          highlightPart(partId);
        }
      });
    }
  }, [showPart, hidePart, highlightPart, removeHighlight]);

  // Function to reset interactive mode
  const resetInteractiveMode = useCallback(() => {
    // Show all parts and remove highlights
    partsListRef.current.forEach(part => {
      showPart(part.id);
      removeHighlight(part.id);
    });
    setInteractiveMode([]);
  }, [showPart, removeHighlight]);

  // Update interactive mode when parts are modified
  const updateInteractiveMode = useCallback((partId: string, updates: Partial<InteractiveModePart>) => {
    setInteractiveMode(prev => {
      const newMode = [...prev];
      const index = newMode.findIndex(m => m.partId === partId);
      
      if (index === -1) {
        // Add new part state
        newMode.push({
          partId,
          isHighlighted: false,
          isIsolated: false,
          isHidden: false,
          ...updates
        });
      } else {
        // Update existing part state
        newMode[index] = { ...newMode[index], ...updates };
      }

      // If this part is being isolated, un-isolate all others
      if (updates.isIsolated) {
        newMode.forEach((m, i) => {
          if (i !== index) m.isIsolated = false;
        });
      }

      // Save to pointer storage if we have a current pointer
      if (currentPointerId) {
        savePointerInteractiveMode(currentPointerId, newMode);
      }

      return newMode;
    });
  }, [currentPointerId, savePointerInteractiveMode]);

  // Modified setCurrentPointerId to handle interactive mode
  const setCurrentPointerId = useCallback((id: string | null) => {
    console.log('ModelViewCore: Setting current pointer:', id);
    
    // Save current interactive mode before switching
    if (currentPointerId) {
      savePointerInteractiveMode(currentPointerId, interactiveMode);
    }

    setCurrentPointerIdState(id);

    // Load interactive mode for new pointer or reset if null
    if (id) {
      const savedMode = loadPointerInteractiveMode(id);
      if (savedMode) {
        setInteractiveMode(savedMode);
        applyInteractiveMode(savedMode);
      } else {
        resetInteractiveMode();
      }
    } else {
      resetInteractiveMode();
    }
  }, [currentPointerId, interactiveMode, savePointerInteractiveMode, loadPointerInteractiveMode, applyInteractiveMode, resetInteractiveMode]);

  // Set current pointer style
  const setCurrentPointerStyle = useCallback((style: PointerStyle | null) => {
    setCurrentPointerStyleState(style);
  }, []);

  // Simple cursor mapping (much cleaner than Iconify approach)
  const getCursorStyle = useCallback((tool: SelectedTool | null): string => {
    if (!tool) return 'default';

    const { mainTool, subTool } = tool;

    // Simple cursor implementation matching your existing project
    switch (mainTool) {
      case 'move':
        if (subTool === 'move-pan') {
          return 'grab';
        }
        if (subTool === 'move-zoom') {
          return 'zoom-in';
        }
        return 'grab';

      case 'pen':
        // Use the pen cursor from your existing project
        return `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 19l7-7 3 3-7 7-3-3z'%3E%3C/path%3E%3Cpath d='M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z'%3E%3C/path%3E%3Cpath d='M2 2l7.586 7.586'%3E%3C/path%3E%3Ccircle cx='11' cy='11' r='2'%3E%3C/circle%3E%3C/svg%3E") 0 24, auto`;

      case 'shape':
      case 'arrow':
        return 'crosshair';

      case 'text':
        return 'text';

      default:
        return 'default';
    }
  }, []);

  // Update canvas cursor
  const updateCanvasCursor = useCallback(() => {
    if (!containerRef.current) {
      return;
    }

    // Try multiple selectors for the X3DOM canvas
    let canvas = containerRef.current.querySelector(
      '.x3dom-canvas',
    ) as HTMLElement;
    if (!canvas) {
      canvas = containerRef.current.querySelector('canvas') as HTMLElement;
    }
    if (!canvas) {
      canvas = containerRef.current.querySelector('x3d') as HTMLElement;
    }
    if (!canvas) {
      return;
    }

    const cursorStyle = getCursorStyle(selectedTool);

    // Apply cursor with !important to override any existing styles
    canvas.style.setProperty('cursor', cursorStyle, 'important');
  }, [selectedTool, currentPointerStyle, getCursorStyle]);

  // Update cursor when tool or style changes
  useEffect(() => {
    updateCanvasCursor();
  }, [updateCanvasCursor]);

  const toggleElement = useCallback(
    (id: string, show: boolean = true): void => {
      const element = containerRef.current?.querySelector(
        `#${id}`,
      ) as HTMLElement;
      if (element) {
        element.style.display = show ? 'block' : 'none';
      }
    },
    [],
  );

  const getElementsByClassName = useCallback(
    (className: string): NodeListOf<Element> => {
      return (
        containerRef.current?.querySelectorAll(`.${className}`) ||
        document.querySelectorAll('')
      );
    },
    [],
  );

  const updateElementAttribute = useCallback(
    (id: string, attribute: string, value: string): void => {
      const element = getElementById(id) as HTMLElement;
      if (element) {
        element.setAttribute(attribute, value);
      }
    },
    [getElementById],
  );

  const updateElementContent = useCallback(
    (id: string, content: string): void => {
      const element = getElementById(id) as HTMLElement;
      if (element) {
        element.innerHTML = content;
      }
    },
    [getElementById],
  );

  const addEventListenerToElement = useCallback(
    (
      id: string,
      event: string,
      handler: EventListener,
    ): (() => void) | undefined => {
      const element = getElementById(id) as HTMLElement;
      if (element) {
        element.addEventListener(event, handler);
        return () => element.removeEventListener(event, handler);
      }
      return undefined;
    },
    [getElementById],
  );

  const togglePart = useCallback(
    (partName: string): void => {
      const part = partsListRef.current.find((p) => p.id === partName);
      if (!part) return;

      const parentElement = part.parentSwitchId
        ? getElementById(part.parentSwitchId)
        : null;
      const currentChoice = parentElement?.getAttribute('whichChoice') || '0';
      const isVisible = currentChoice !== '-1';

      if (isVisible) {
        hidePart(partName);
      } else {
        showPart(partName);
      }
    },
    [getElementById, hidePart, showPart],
  );

  const removeHighlight = useCallback(
    (partName: string): void => {
      const part = partsListRef.current.find((p) => p.id === partName);
      if (!part) return;

      const highlightSwitches = [
        part.parentSwitchId,
        ...(part.relatedSwitchIds || []),
      ];

      highlightSwitches.forEach((switchId) => {
        if (!switchId) return;

        const switchElement = getElementById(switchId);
        if (switchElement) {
          const app0 = getElementById('mat0');
          const originalColor =
            app0?.getAttribute('diffusecolor') || '0.8 0.8 0.8';
          // Remove created materials
          const createdMaterials = switchElement.querySelectorAll(
            'material[data-created-for-highlight="true"]',
          );
          createdMaterials.forEach((material) => {
            material.setAttribute('diffusecolor', originalColor);
            material.removeAttribute('data-original-color');
          });
        }
      });
    },
    [getElementById],
  );

  const setPartAttribute = useCallback(
    (partName: string, attribute: string, value: string): void => {
      const part = partsListRef.current.find((p) => p.id === partName);
      if (!part) return;

      if (part.parentSwitchId) {
        updateElementAttribute(part.parentSwitchId, attribute, value);
      }

      part.relatedSwitchIds?.forEach((switchId) => {
        updateElementAttribute(switchId, attribute, value);
      });
    },
    [updateElementAttribute],
  );

  // Modified part operations to use interactive mode
  const handlePartHide = useCallback((partName: string) => {
    const isHidden = interactiveMode.find(m => m.partId === partName)?.isHidden;
    updateInteractiveMode(partName, { isHidden: !isHidden });
  }, [interactiveMode, updateInteractiveMode]);

  const handlePartHighlight = useCallback((partName: string) => {
    const isHighlighted = interactiveMode.find(m => m.partId === partName)?.isHighlighted;
    updateInteractiveMode(partName, { isHighlighted: !isHighlighted });
  }, [interactiveMode, updateInteractiveMode]);

  const handlePartIsolate = useCallback((partName: string) => {
    const isIsolated = interactiveMode.find(m => m.partId === partName)?.isIsolated;
    updateInteractiveMode(partName, { isIsolated: !isIsolated });
  }, [interactiveMode, updateInteractiveMode]);

  const handleShowAllParts = useCallback(() => {
    resetInteractiveMode();
  }, [resetInteractiveMode]);

  // Effect to apply interactive mode when it changes
  useEffect(() => {
    if (interactiveMode.length > 0) {
      applyInteractiveMode(interactiveMode);
    }
  }, [interactiveMode, applyInteractiveMode]);

  // Handle part deletion
  const handlePartDelete = useCallback((partName: string) => {
    console.log('Deleting part:', partName);
    // Implementation depends on your application's requirements
    // This is a placeholder
  }, []);

  // Check if a part is hidden
  const isPartHidden = useCallback(
    (partName: string) => {
      return hiddenParts.has(partName);
    },
    [hiddenParts],
  );

  // Check if a part is highlighted
  const isPartHighlighted = useCallback(
    (partName: string) => {
      return highlightedParts.has(partName);
    },
    [highlightedParts],
  );

  // Check if a part is isolated
  const isPartIsolated = useCallback(
    (partName: string) => {
      return isolatedPartName === partName;
    },
    [isolatedPartName],
  );

  const handleShapeClick = useCallback(
    (shape: Element, event: MouseEvent): void => {
      if (isolationMode && event.button === 0) {
        handleShowAllParts();
        return;
      }
      // Check if it's a right-click (button 2) or left-click (button 0)
      let partName = shape.getAttribute('id');
      if (shape) {
        const parentGroup = shape.closest('Group[DEF]');
        if (parentGroup) {
          const defAttribute = parentGroup.getAttribute('DEF');
          if (defAttribute) {
            partName = defAttribute;
            console.log('Using parent Group DEF:', partName);
          }
        }
      }
      if (event.button === 2) {
        const containerRect = containerRef.current?.getBoundingClientRect();
        const x = (event.layerX || 0) + (containerRect?.left || 0);
        const y = (event.layerY || 0) + (containerRect?.top || 0);
        // Show context menu at the mouse position
        setContextMenu({
          isOpen: true,
          position: { x, y },
          partName: partName as string,
        });
      } else if (event.button === 0) {
        // Close context menu on left click
        setContextMenu((prev) => ({ ...prev, isOpen: false }));
        // Handle left-click specific logic here
      }
    },
    [getElementById, isolationMode, handleShowAllParts, contextMenu],
  );

  // Add click handler to close context menu when clicking outside
  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      const contextMenuElement = document.getElementById('part-context-menu');
      if (
        contextMenu.isOpen &&
        contextMenuElement &&
        !contextMenuElement.contains(event.target as Node)
      ) {
        setContextMenu((prev) => ({ ...prev, isOpen: false }));
      }
    };

    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, [contextMenu.isOpen]);

  const addShapeClickHandlers = useCallback((): void => {
    if (!containerRef.current) return;

    const shapes = containerRef.current.querySelectorAll('shape');
    shapes.forEach((shape) => {
      const shapeId = shape.getAttribute('id');
      if (shapeId) {
        // Handle left-click (normal click)
        shape.addEventListener('click', (event) =>
          handleShapeClick(shape, event as MouseEvent),
        );

        // Handle right-click (contextmenu)
        shape.addEventListener('contextmenu', (event) => {
          event.preventDefault(); // Prevent default context menu
          handleShapeClick(shape, event as MouseEvent);
        });
      }
    });
  }, [handleShapeClick]);

  // Update partOperations to include the new function
  const partOperations = useMemo<PartOperations>(
    () => ({
      showPart,
      hidePart,
      togglePart,
      highlightPart,
      removeHighlight,
      setPartAttribute,
      addShapeClickHandlers,
    }),
    [
      showPart,
      hidePart,
      togglePart,
      highlightPart,
      removeHighlight,
      setPartAttribute,
      addShapeClickHandlers,
    ],
  );

  useEffect(() => {
    if (initializedRef.current) return;
    initializedRef.current = true;

    const loadDependencies = async () => {
      if (
        document.querySelector('link[href*="x3dom.css"]') &&
        document.querySelector('script[src*="x3dom.js"]') &&
        document.querySelector('script[src*="jquery"]')
      ) {
        setScriptsLoaded(true);
        return;
      }

      if (!document.querySelector('link[href*="x3dom.css"]')) {
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.type = 'text/css';
        cssLink.href =
          'https://cdn.jsdelivr.net/gh/x3dom/x3dom-stable@1.8.3/dist/x3dom.css';
        document.head.appendChild(cssLink);
      }

      const loadScript = (src: string): Promise<void> => {
        return new Promise((resolve, reject) => {
          if (document.querySelector(`script[src="${src}"]`)) {
            resolve();
            return;
          }

          const script = document.createElement('script');
          script.type = 'text/javascript';
          script.src = src;
          script.onload = () => resolve();
          script.onerror = () =>
            reject(new Error(`Failed to load script: ${src}`));
          document.head.appendChild(script);
        });
      };

      try {
        await loadScript(
          'https://cdn.jsdelivr.net/gh/x3dom/x3dom-stable@1.8.3/dist/x3dom.js',
        );
        await loadScript('https://code.jquery.com/jquery-2.1.0.min.js');
        setScriptsLoaded(true);
      } catch (error) {
        console.error('Error loading external dependencies:', error);
        setScriptsLoaded(true);
      }
    };

    loadDependencies();
  }, []);

  // Update initializeModel to add click handlers after model loads
  const initializeModel = useCallback(
    (htmlString: string) => {
      if (!htmlString || !containerRef.current || !scriptsLoaded) return;

      // Clear any existing timeouts
      if (initializedRef.current) {
        return;
      }

      if (containerRef.current.innerHTML) {
        containerRef.current.innerHTML = '';
      }

      containerRef.current.innerHTML = htmlString;

      let timeoutId: NodeJS.Timeout;
      let x3domTimeoutId: NodeJS.Timeout;

      const cleanup = () => {
        if (timeoutId) clearTimeout(timeoutId);
        if (x3domTimeoutId) clearTimeout(x3domTimeoutId);
        initializedRef.current = false;
      };

      timeoutId = setTimeout(() => {
        if (!containerRef.current) {
          cleanup();
          return;
        }

        setIsLoaded(true);

        const defGroups =
          containerRef.current?.querySelectorAll('Group[DEF]') || [];
        const parts: Part[] = [];

        defGroups.forEach((defGroup, index) => {
          const partName = defGroup.getAttribute('DEF');
          if (!partName) return;

          const parentSwitch = defGroup.closest('Switch');
          const parentSwitchId = parentSwitch?.getAttribute('id') || '';

          const useGroups = containerRef.current!.querySelectorAll(
            `Group[USE="${partName}"]`,
          );
          const relatedSwitchIds: string[] = [];

          useGroups.forEach((useGroup) => {
            const switchParent = useGroup.closest('Switch');
            const switchId = switchParent?.getAttribute('id');
            if (switchId && !relatedSwitchIds.includes(switchId)) {
              relatedSwitchIds.push(switchId);
            }
          });

          const isFirstPart = index === 0;
          parts.push({
            id: partName,
            name: partName
              .replace(/_/g, ' ')
              .replace(/([A-Z])/g, ' $1')
              .trim(),
            category: 'Structural' as PartCategory,
            specifications: {
              type: '3D Model Part',
            },
            parentSwitchId,
            relatedSwitchIds,
            rank: isFirstPart ? 1 : index + 1,
            visible: !isFirstPart,
          });
        });

        // Batch state updates
        Promise.resolve().then(() => {
          setPartsList(parts);
          partsListRef.current = parts;

          const functions: ControlFunctions = {
            toggleElement,
            getElementById,
            getElementsByClassName,
            updateElementAttribute,
            updateElementContent,
            addEventListenerToElement,
            container: containerRef.current,
            getPartsList: () =>
              partsListRef.current.filter((part) => part.visible),
            partOperations,
          };

          setControlFunctions(functions);

          if (typeof (window as any).x3dom !== 'undefined') {
            (window as any).x3dom.reload();
            // Add click handlers and apply initial cursor after x3dom reloads
            x3domTimeoutId = setTimeout(() => {
              if (containerRef.current) {
                partOperations.addShapeClickHandlers();
                updateCanvasCursor();
              }
            }, 200);
          }
        });
      }, 100);

      return cleanup;
    },
    [
      scriptsLoaded,
      toggleElement,
      getElementById,
      getElementsByClassName,
      updateElementAttribute,
      updateElementContent,
      addEventListenerToElement,
      partOperations,
      updateCanvasCursor,
    ],
  );

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(
    () => ({
      partsList,
      setPartsList,
      controlFunctions,
      setControlFunctions,
      selectedPart,
      setSelectedPart,
      containerRef,
      scriptsLoaded,
      isLoaded,
      initializeModel,
      selectedTool,
      setSelectedTool,
      currentPointerId,
      setCurrentPointerId,
      currentPointerStyle,
      setCurrentPointerStyle,
      updateCanvasCursor,
      saveInteractiveMode,
      loadInteractiveMode,
      clearInteractiveMode,
      interactiveMode,
      setInteractiveMode,
      applyInteractiveMode,
      resetInteractiveMode,
    }),
    [
      partsList,
      controlFunctions,
      selectedPart,
      scriptsLoaded,
      isLoaded,
      initializeModel,
      selectedTool,
      currentPointerId,
      currentPointerStyle,
      updateCanvasCursor,
      saveInteractiveMode,
      loadInteractiveMode,
      clearInteractiveMode,
      interactiveMode,
      setInteractiveMode,
      applyInteractiveMode,
      resetInteractiveMode,
    ],
  );

  return (
    <ModelViewCoreContext.Provider value={value}>
      {children}
      {contextMenu.isOpen && (
        <PartContextMenu
          isOpen={contextMenu.isOpen}
          onClose={() => setContextMenu((prev) => ({ ...prev, isOpen: false }))}
          position={contextMenu.position}
          onHidePart={handlePartHide}
          onIsolatePart={handlePartIsolate}
          onHighlightPart={() => handlePartHighlight(contextMenu.partName)}
          onDeletePart={handlePartDelete}
          partName={contextMenu.partName}
          isHidden={interactiveMode.find(m => m.partId === contextMenu.partName)?.isHidden || false}
          isIsolated={interactiveMode.find(m => m.partId === contextMenu.partName)?.isIsolated || false}
          isHighlighted={interactiveMode.find(m => m.partId === contextMenu.partName)?.isHighlighted || false}
          onShowAllParts={handleShowAllParts}
        />
      )}
    </ModelViewCoreContext.Provider>
  );
};

export const useModelViewCore = () => {
  const context = useContext(ModelViewCoreContext);
  if (context === undefined) {
    throw new Error(
      'useModelViewCore must be used within a ModelViewCoreProvider',
    );
  }
  return context;
};
